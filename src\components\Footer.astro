---
// Enhanced Footer component data
const currentYear = new Date().getFullYear();

const quickLinks = [
  { text: "Features", url: "/#features" },
  { text: "Pricing", url: "/#pricing" },
  { text: "QR Generator", url: "/tool/qr-code-generator" },
  { text: "Dashboard", url: "/dashboard" },
];

const resourceLinks = [
  { text: "Help Center", url: "#" },
  { text: "API Documentation", url: "#" },
  { text: "Tutorials", url: "#" },
  { text: "Blog", url: "#" },
];

const legalLinks = [
  { text: "Privacy Policy", url: "#" },
  { text: "Terms of Service", url: "#" },
  { text: "Cookie Policy", url: "#" },
];

const socialLinks = [
  {
    name: "X (Twitter)",
    url: "https://x.com/qranalytica",
    icon: "M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
  },
  {
    name: "YouTube",
    url: "https://www.youtube.com/@QRAnalytica",
    icon: "M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"
  },
  {
    name: "LinkedIn",
    url: "https://www.linkedin.com/company/qranalytica",
    icon: "M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
  },
  // {
  //   name: "GitHub",
  //   url: "https://github.com/qranalytica",
  //   icon: "M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"
  // }
];
---

<footer class="bg-gray-50 border-t border-gray-200">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">

      <!-- Brand Section -->
      <div class="lg:col-span-2">
        <a href="/" class="flex items-center space-x-3 group mb-6">
          <div class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM3 21h8v-8H3v8zm2-6h4v4H5v-4z"/>
              <path d="M16 16h2v2h-2v-2zM18 18h2v2h-2v-2zM16 20h2v2h-2v-2zM20 16h2v2h-2v-2zM20 20h2v2h-2v-2z"/>
            </svg>
          </div>
          <span class="font-bold text-xl text-gray-900">QRAnalytica</span>
        </a>

        <p class="text-gray-600 text-sm leading-relaxed max-w-sm mb-8">
          Professional QR code analytics platform. Create, track, and optimize your QR campaigns with comprehensive insights and real-time data.
        </p>

        <!-- Newsletter Signup -->
        <!-- <div class="mb-8">
          <h4 class="font-semibold text-gray-900 text-sm mb-4">Stay Updated</h4>
          <div class="flex space-x-2">
            <input
              type="email"
              placeholder="Enter your email"
              class="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
            <button
              class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-300"
            >
              Subscribe
            </button>
          </div>
        </div> -->

        <!-- Social Links -->
        <div class="flex items-center space-x-4">
          {socialLinks.map((social) => (
            <a
              href={social.url}
              target="_blank"
              rel="noopener noreferrer"
              class="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg"
              aria-label={social.name}
            >
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d={social.icon} />
              </svg>
            </a>
          ))}
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h4 class="font-semibold text-gray-900 text-sm mb-6">
          Product
        </h4>
        <ul class="space-y-4">
          {quickLinks.map((link) => (
            <li>
              <a
                href={link.url}
                class="text-gray-600 hover:text-gray-900 text-sm transition-colors"
                target={link.url.startsWith('http') ? '_blank' : undefined}
                rel={link.url.startsWith('http') ? 'noopener noreferrer' : undefined}
              >
                {link.text}
              </a>
            </li>
          ))}
        </ul>
      </div>

      <!-- Resources -->
      <!-- <div>
        <h4 class="font-semibold text-gray-900 text-sm mb-6">
          Resources
        </h4>
        <ul class="space-y-4">
          {resourceLinks.map((link) => (
            <li>
              <a
                href={link.url}
                class="text-gray-600 hover:text-gray-900 text-sm transition-colors"
                target={link.url.startsWith('http') ? '_blank' : undefined}
                rel={link.url.startsWith('http') ? 'noopener noreferrer' : undefined}
              >
                {link.text}
              </a>
            </li>
          ))}
        </ul>
      </div> -->

      <!-- Contact & Legal -->
      <div>
        <h4 class="font-semibold text-gray-900 text-sm mb-6">
          Support
        </h4>
        <div class="space-y-4 mb-8">
          <a
            href="mailto:<EMAIL>"
            class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors text-sm group"
          >
            <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
            <span>Contact Support</span>
          </a>

          <button
            onclick="openSalesChat()"
            class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors text-sm group"
          >
            <svg class="w-4 h-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
            <span>Live Chat</span>
          </button>
        </div>

        <!-- Legal Links -->
        <!-- <div class="space-y-3">
          {legalLinks.map((link) => (
            <a
              href={link.url}
              class="block text-gray-500 hover:text-gray-700 text-xs transition-colors"
            >
              {link.text}
            </a>
          ))}
        </div> -->
      </div>
    </div>

    <!-- Bottom Bar -->
    <div class="mt-16 pt-8 border-t border-gray-200">
      <div class="flex flex-col md:flex-row justify-between items-center gap-4">
        <div class="flex flex-col md:flex-row items-center gap-4 text-sm text-gray-500">
          <p>© {currentYear} QRAnalytica. All rights reserved.</p>
          <div class="flex items-center space-x-1">
            <span>Made with</span>
            <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd" />
            </svg>
            <span>by</span>
            <a
              href="https://www.linkedin.com/in/nookeshkarri"
              target="_blank"
              rel="noopener noreferrer"
              class="text-blue-600 hover:text-blue-800 transition-colors font-medium"
            >
              Nookesh Karri
            </a>
          </div>
        </div>

        <div class="flex items-center space-x-6 text-sm">
          <div class="flex items-center space-x-2 text-gray-500">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>All systems operational</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</footer>

<script>
  // Enhanced newsletter subscription functionality
  document.addEventListener('DOMContentLoaded', function() {
    const footer = document.querySelector('footer');
    const subscribeButton = footer?.querySelector('button') as HTMLButtonElement;
    const emailInput = footer?.querySelector('input[type="email"]') as HTMLInputElement;

    if (subscribeButton && emailInput) {
      subscribeButton.addEventListener('click', function(e: Event) {
        e.preventDefault();
        const email = emailInput.value.trim();

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (email && emailRegex.test(email)) {
          // Show loading state
          const originalText = subscribeButton.textContent;
          subscribeButton.textContent = 'Subscribing...';
          subscribeButton.disabled = true;

          // Simulate API call (replace with actual newsletter service)
          setTimeout(() => {
            console.log('Newsletter signup:', email);
            subscribeButton.textContent = '✓ Subscribed!';
            subscribeButton.className = subscribeButton.className.replace('from-blue-600 to-purple-600', 'from-green-600 to-green-600');
            emailInput.value = '';

            // Reset after 3 seconds
            setTimeout(() => {
              subscribeButton.textContent = originalText;
              subscribeButton.disabled = false;
              subscribeButton.className = subscribeButton.className.replace('from-green-600 to-green-600', 'from-blue-600 to-purple-600');
            }, 3000);
          }, 1000);
        } else {
          // Show error state
          emailInput.classList.add('border-red-500', 'focus:ring-red-500');
          emailInput.placeholder = 'Please enter a valid email';

          setTimeout(() => {
            emailInput.classList.remove('border-red-500', 'focus:ring-red-500');
            emailInput.placeholder = 'Enter your email';
          }, 3000);
        }
      });

      // Handle Enter key
      emailInput.addEventListener('keypress', function(e: KeyboardEvent) {
        if (e.key === 'Enter') {
          subscribeButton.click();
        }
      });

      // Clear error state on input
      emailInput.addEventListener('input', function() {
        emailInput.classList.remove('border-red-500', 'focus:ring-red-500');
        if (emailInput.placeholder === 'Please enter a valid email') {
          emailInput.placeholder = 'Enter your email';
        }
      });
    }
  });
</script>