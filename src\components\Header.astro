---

---

<header
  class="bg-white/95 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-50 w-full shadow-sm"
>
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center py-4">
      <!-- Enhanced Professional Logo -->
      <a href="/" class="flex items-center space-x-3 group">
        <div
          class="w-10 h-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-105"
        >
          <svg
            class="w-5 h-5 text-white"
            fill="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              d="M3 11h8V3H3v8zm2-6h4v4H5V5zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM3 21h8v-8H3v8zm2-6h4v4H5v-4z"
            ></path>
            <path
              d="M16 16h2v2h-2v-2zM18 18h2v2h-2v-2zM16 20h2v2h-2v-2zM20 16h2v2h-2v-2zM20 20h2v2h-2v-2z"
            ></path>
          </svg>
        </div>
        <div class="flex flex-col">
          <span class="font-bold text-lg text-gray-900 tracking-tight"
            >QRAnalytica</span
          >
          <span
            class="text-xs text-gray-500 -mt-0.5 hidden sm:block font-medium"
            >QR Analytics Platform</span
          >
        </div>
      </a>

      <!-- Enhanced Professional Desktop Navigation -->
      <nav class="hidden lg:flex items-center space-x-8">
        <a
          href="/#features"
          class="text-gray-600 hover:text-gray-900 transition-colors duration-200 font-medium text-sm relative group py-2 px-3 rounded-lg hover:bg-gray-50"
        >
          Features
          <span
            class="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-300 group-hover:w-6 rounded-full"
          ></span>
        </a>
        <a
          href="/#pricing"
          class="text-gray-600 hover:text-gray-900 transition-colors duration-200 font-medium text-sm relative group py-2 px-3 rounded-lg hover:bg-gray-50"
        >
          Pricing
          <span
            class="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-300 group-hover:w-6 rounded-full"
          ></span>
        </a>
        <a
          href="/tool/qr-code-generator"
          class="text-gray-600 hover:text-gray-900 transition-colors duration-200 font-medium text-sm relative group py-2 px-3 rounded-lg hover:bg-gray-50"
        >
          QR Generator
          <span
            class="absolute -bottom-0.5 left-1/2 transform -translate-x-1/2 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-300 group-hover:w-6 rounded-full"
          ></span>
        </a>
      </nav>

      <!-- Enhanced Professional Authentication Section -->
      <div class="flex items-center space-x-4">
        <!-- Authentication Buttons - Desktop -->
        <div
          id="auth-buttons-desktop"
          class="hidden lg:flex items-center space-x-4"
        >
          <!-- Unauthenticated State -->
          <div id="unauthenticated-desktop" class="flex items-center space-x-4">
            <button
              id="google-signin-desktop"
              class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-all duration-200 font-medium text-sm px-4 py-2 rounded-lg hover:bg-gray-50 border border-gray-200 hover:border-gray-300"
            >
              <svg class="w-4 h-4" viewBox="0 0 24 24">
                <path
                  fill="#4285F4"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                ></path>
                <path
                  fill="#34A853"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                ></path>
                <path
                  fill="#FBBC05"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                ></path>
                <path
                  fill="#EA4335"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                ></path>
              </svg>
              <span>Sign In</span>
            </button>
          </div>

          <!-- Authenticated State -->
          <div id="authenticated-desktop" class="hidden items-center space-x-3">
            <!-- User Profile -->
            <div
              class="flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50 border border-gray-200"
            >
              <img
                id="user-avatar-desktop"
                src=""
                alt="User"
                class="w-8 h-8 rounded-full border-2 border-blue-500 shadow-sm"
              />
              <div class="flex flex-col">
                <span
                  id="user-name-desktop"
                  class="text-sm font-semibold text-gray-900"></span>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center space-x-2">
              <a
                href="/dashboard"
                class="text-gray-600 hover:text-gray-900 transition-colors duration-200 font-medium text-sm px-3 py-2 rounded-lg hover:bg-gray-50"
              >
                Dashboard
              </a>
              <button
                id="signout-desktop"
                class="text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium text-sm px-3 py-2 rounded-lg hover:bg-red-50"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>

        <!-- Enhanced Primary CTA Button -->
        <a href="/tool/qr-code-generator" class="hidden sm:inline-flex">
          <button
            class="bg-gradient-to-r from-[#18BC9C] to-[#16A085] hover:from-[#16A085] hover:to-[#18BC9C] text-black px-7 py-3 rounded-xl font-semibold text-sm shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 border border-[#18BC9C]/20"
          >
            Create QR Code
          </button>
        </a>

        <!-- Enhanced Mobile Menu Button -->
        <button
          id="mobile-menu-button"
          class="lg:hidden inline-flex items-center justify-center p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200 border border-gray-200"
          aria-expanded="false"
          aria-controls="mobile-menu"
        >
          <span class="sr-only">Open navigation menu</span>
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Enhanced Mobile Menu -->
    <div id="mobile-menu" class="lg:hidden hidden">
      <div class="border-t border-gray-200 bg-white/95 backdrop-blur-lg">
        <div class="p-4 space-y-4">
          <!-- Navigation Links -->
          <div class="space-y-1">
            <a
              href="/#features"
              class="block text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200 font-medium px-4 py-3 rounded-lg text-sm"
            >
              Features
            </a>
            <a
              href="/#pricing"
              class="block text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200 font-medium px-4 py-3 rounded-lg text-sm"
            >
              Pricing
            </a>
            <a
              href="/tool/qr-code-generator"
              class="block text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-all duration-200 font-medium px-4 py-3 rounded-lg text-sm"
            >
              QR Generator
            </a>
          </div>

          <!-- Mobile Authentication Section -->
          <div class="border-t border-gray-200 pt-4 space-y-4">
            <!-- Unauthenticated State Mobile -->
            <div id="unauthenticated-mobile" class="space-y-3">
              <button
                id="google-signin-mobile"
                class="flex items-center justify-center space-x-2 w-full text-gray-600 hover:text-gray-900 transition-all duration-200 font-medium text-sm py-3 border border-gray-200 rounded-lg hover:bg-gray-50"
              >
                <svg class="w-4 h-4" viewBox="0 0 24 24">
                  <path
                    fill="#4285F4"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  ></path>
                  <path
                    fill="#34A853"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  ></path>
                  <path
                    fill="#FBBC05"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  ></path>
                  <path
                    fill="#EA4335"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  ></path>
                </svg>
                <span>Sign In with Google</span>
              </button>
            </div>

            <!-- Authenticated State Mobile -->
            <div id="authenticated-mobile" class="hidden space-y-3">
              <!-- User Profile Card -->
              <div
                class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg border border-gray-200"
              >
                <img
                  id="user-avatar-mobile"
                  src=""
                  alt="User"
                  class="w-10 h-10 rounded-full border-2 border-blue-500 shadow-sm"
                />
                <div class="flex flex-col">
                  <span
                    id="user-name-mobile"
                    class="text-sm font-semibold text-gray-900"></span>
                  <div class="flex items-center space-x-1">
                    <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span class="text-xs text-green-600 font-medium"
                      >Active</span
                    >
                  </div>
                </div>
              </div>

              <!-- Action Links -->
              <div class="space-y-2">
                <a
                  href="/dashboard"
                  class="block text-center text-gray-600 hover:text-gray-900 transition-all duration-200 font-medium text-sm py-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  Dashboard
                </a>
                <button
                  id="signout-mobile"
                  class="w-full text-center text-gray-500 hover:text-red-600 transition-all duration-200 font-medium text-sm py-3 border border-gray-200 rounded-lg hover:bg-red-50"
                >
                  Sign Out
                </button>
              </div>
            </div>

            <!-- Primary CTA -->
            <a href="/tool/qr-code-generator" class="block">
              <button
                class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-lg font-semibold text-sm shadow-md transition-all duration-300"
              >
                Create QR Code
              </button>
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Professional Trust Bar -->
  <!-- <div class="bg-gradient-to-r from-slate-50/50 to-white border-b border-gray-50">
    <div class="max-w-7xl mx-auto px-6 py-3">
      <div class="flex items-center justify-center space-x-12 text-xs text-gray-600">
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-[#18BC9C]" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
          </svg>
          <span class="font-medium">Enterprise Grade Security</span>
        </div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-[#18BC9C]" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"/>
          </svg>
          <span class="font-medium">Real-Time Analytics</span>
        </div>
        <div class="flex items-center space-x-2">
          <svg class="w-4 h-4 text-[#18BC9C]" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 1.414L10.586 9.5 9.293 10.793a1 1 0 101.414 1.414l2-2a1 1 0 000-1.414z" clip-rule="evenodd"/>
          </svg>
          <span class="font-medium">99.9% Uptime SLA</span>
        </div>
      </div>
    </div>
  </div> -->
</header>

<script>
  // Enhanced mobile menu and authentication functionality
  document.addEventListener("DOMContentLoaded", function () {
    const mobileMenuButton = document.getElementById("mobile-menu-button");
    const mobileMenu = document.getElementById("mobile-menu");

    // Authentication UI elements
    const unauthenticatedDesktop = document.getElementById(
      "unauthenticated-desktop"
    );
    const authenticatedDesktop = document.getElementById(
      "authenticated-desktop"
    );
    const unauthenticatedMobile = document.getElementById(
      "unauthenticated-mobile"
    );
    const authenticatedMobile = document.getElementById("authenticated-mobile");

    const googleSigninDesktop = document.getElementById(
      "google-signin-desktop"
    );
    const googleSigninMobile = document.getElementById("google-signin-mobile");
    const signoutDesktop = document.getElementById("signout-desktop");
    const signoutMobile = document.getElementById("signout-mobile");

    const userAvatarDesktop = document.getElementById("user-avatar-desktop");
    const userNameDesktop = document.getElementById("user-name-desktop");
    const userAvatarMobile = document.getElementById("user-avatar-mobile");
    const userNameMobile = document.getElementById("user-name-mobile");

    // Simple cookie utility
    function getCookie(name: string): string | null {
      const cookies = document.cookie ? document.cookie.split("; ") : [];
      for (const c of cookies) {
        const [k, ...rest] = c.split("=");
        if (k === name) return rest.join("=");
      }
      return null;
    }

    // Check authentication state
    function checkAuthState() {
      try {
        const rawCookie = getCookie("user");
        if (rawCookie) {
          let jsonStr;
          try {
            const firstDecode = decodeURIComponent(rawCookie);
            jsonStr = decodeURIComponent(firstDecode);
          } catch {
            jsonStr = rawCookie;
          }

          const user = JSON.parse(jsonStr);
          return { authenticated: true, user };
        }
      } catch (error) {
        console.error("Error checking auth state:", error);
      }
      return { authenticated: false, user: null };
    }

    // Update UI based on auth state
    function updateAuthUI() {
      const authState = checkAuthState();

      if (authState.authenticated && authState.user) {
        // Show authenticated state
        if (unauthenticatedDesktop)
          unauthenticatedDesktop.style.display = "none";
        if (authenticatedDesktop) authenticatedDesktop.style.display = "flex";
        if (unauthenticatedMobile) unauthenticatedMobile.style.display = "none";
        if (authenticatedMobile) authenticatedMobile.style.display = "block";

        // Update user info
        const user = authState.user;
        if (userAvatarDesktop)
          (userAvatarDesktop as HTMLImageElement).src =
            user.picture || "/default-avatar.png";
        if (userNameDesktop)
          userNameDesktop.textContent = user.name || user.email;
        if (userAvatarMobile)
          (userAvatarMobile as HTMLImageElement).src =
            user.picture || "/default-avatar.png";
        if (userNameMobile)
          userNameMobile.textContent = user.name || user.email;
      } else {
        // Show unauthenticated state
        if (unauthenticatedDesktop)
          unauthenticatedDesktop.style.display = "flex";
        if (authenticatedDesktop) authenticatedDesktop.style.display = "none";
        if (unauthenticatedMobile)
          unauthenticatedMobile.style.display = "block";
        if (authenticatedMobile) authenticatedMobile.style.display = "none";
      }
    }

    // Initial auth UI update
    updateAuthUI();

    // Sign in handlers
    function handleSignIn() {
      window.location.href = "/api/auth/google";
    }

    if (googleSigninDesktop) {
      googleSigninDesktop.addEventListener("click", handleSignIn);
    }

    if (googleSigninMobile) {
      googleSigninMobile.addEventListener("click", handleSignIn);
    }

    // Sign out handlers
    function handleSignOut() {
      document.cookie = "user=; Max-Age=0; path=/";
      window.location.reload();
    }

    if (signoutDesktop) {
      signoutDesktop.addEventListener("click", handleSignOut);
    }

    if (signoutMobile) {
      signoutMobile.addEventListener("click", handleSignOut);
    }

    // Mobile menu functionality
    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener("click", function () {
        const isExpanded =
          mobileMenuButton.getAttribute("aria-expanded") === "true";

        mobileMenuButton.setAttribute(
          "aria-expanded",
          (!isExpanded).toString()
        );
        mobileMenu.classList.toggle("hidden");

        // Smooth icon transition
        const icon = mobileMenuButton.querySelector("svg");
        if (icon) {
          icon.style.transform = isExpanded ? "rotate(0deg)" : "rotate(90deg)";

          if (isExpanded) {
            icon.innerHTML =
              '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>';
          } else {
            icon.innerHTML =
              '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>';
          }
        }
      });

      // Close mobile menu when clicking outside
      document.addEventListener("click", function (event) {
        const target = event.target as Node;
        if (
          !mobileMenuButton.contains(target) &&
          !mobileMenu.contains(target)
        ) {
          mobileMenu.classList.add("hidden");
          mobileMenuButton.setAttribute("aria-expanded", "false");
          const icon = mobileMenuButton.querySelector("svg");
          if (icon) {
            icon.style.transform = "rotate(0deg)";
            icon.innerHTML =
              '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>';
          }
        }
      });
    }
  });
</script>

<style>
  /* Enhanced professional header styling */
  header {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(229, 231, 235, 0.6);
    background: rgba(255, 255, 255, 0.95);
  }

  /* Smooth animations with modern easing */
  * {
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Professional logo hover effect */
  .group:hover .group-hover\:scale-105 {
    transform: scale(1.05);
  }

  /* Subtle button hover effects */
  button:hover {
    transform: translateY(-1px);
  }

  /* Enhanced mobile menu animation */
  #mobile-menu {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: top;
  }

  #mobile-menu.hidden {
    transform: scaleY(0.95) translateY(-8px);
    opacity: 0;
  }

  /* Modern navigation underline animation */
  .group:hover span {
    width: 24px;
    background: linear-gradient(90deg, #2563eb, #7c3aed);
  }

  /* Enhanced authentication UI */
  #authenticated-desktop > div:first-child,
  #authenticated-mobile .bg-gray-50 {
    background: rgba(249, 250, 251, 1);
    border: 1px solid rgba(229, 231, 235, 1);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* Modern hover states */
  .hover\:bg-gray-50:hover {
    background-color: rgba(249, 250, 251, 1);
  }

  .hover\:bg-red-50:hover {
    background-color: rgba(254, 242, 242, 1);
  }

  /* Enhanced button shadows */
  .shadow-md {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  /* Professional gradient buttons */
  .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
  }
</style>

